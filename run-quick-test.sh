#!/bin/bash

echo "🚀 Quick MCP Server Test"
echo "======================="
echo ""

# Test 1: List Tools
echo "🔧 Test 1: Listing available tools..."
RESPONSE1=$(echo '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' | timeout 5s node mcp-thinking.js 2>/dev/null)
if [[ $? -eq 0 && "$RESPONSE1" == *"tools"* ]]; then
    echo "✅ Tools list: PASSED"
    TOOL_COUNT=$(echo "$RESPONSE1" | grep -o '"name"' | wc -l)
    echo "   Found $TOOL_COUNT tools"
else
    echo "❌ Tools list: FAILED"
fi
echo ""

# Test 2: Get Code Quality Rules
echo "🔧 Test 2: Getting code quality rules..."
RESPONSE2=$(echo '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"get_code_quality_rules","arguments":{"category":"function_length"}}}' | timeout 5s node mcp-thinking.js 2>/dev/null)
if [[ $? -eq 0 && "$RESPONSE2" == *"Function Length Standards"* ]]; then
    echo "✅ Code quality rules: PASSED"
else
    echo "❌ Code quality rules: FAILED"
fi
echo ""

# Test 3: Validate Code
echo "🔧 Test 3: Validating code snippet..."
RESPONSE3=$(echo '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"validate_code_against_patterns","arguments":{"code":"function calc(a,b,c,d,e){return a+b+c+d+e;}","language":"javascript"}}}' | timeout 5s node mcp-thinking.js 2>/dev/null)
if [[ $? -eq 0 && "$RESPONSE3" == *"analysis"* ]]; then
    echo "✅ Code validation: PASSED"
else
    echo "❌ Code validation: FAILED"
fi
echo ""

# Test 4: Error Handling Strategy
echo "🔧 Test 4: Getting error handling strategy..."
RESPONSE4=$(echo '{"jsonrpc":"2.0","id":4,"method":"tools/call","params":{"name":"get_error_handling_strategy","arguments":{"context":"user_facing"}}}' | timeout 5s node mcp-thinking.js 2>/dev/null)
if [[ $? -eq 0 && "$RESPONSE4" == *"error"* ]]; then
    echo "✅ Error handling strategy: PASSED"
else
    echo "❌ Error handling strategy: FAILED"
fi
echo ""

echo "🎉 Quick test completed!"
echo ""
echo "💡 For more detailed testing:"
echo "   - Run: node test-mcp.js (interactive test)"
echo "   - Check: ./manual-test.sh (manual testing guide)"
