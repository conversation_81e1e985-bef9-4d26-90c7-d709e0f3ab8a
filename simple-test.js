#!/usr/bin/env node

/**
 * Simple test to verify MCP SDK is working
 */

console.log('🔍 Testing MCP SDK import...');

try {
  // Test import
  import('@modelcontextprotocol/sdk/server/index.js').then((module) => {
    console.log('✅ MCP SDK imported successfully!');
    console.log('📦 Available exports:', Object.keys(module));
    
    // Test basic server creation
    const { Server } = module;
    const server = new Server(
      { name: 'test-server', version: '1.0.0' },
      { capabilities: { tools: {} } }
    );
    
    console.log('✅ Server created successfully!');
    console.log('🎯 Server name:', server.name);
    
    console.log('\n🚀 MCP SDK is working correctly!');
    console.log('💡 You can now run: node mcp-thinking.js');
    
  }).catch((error) => {
    console.error('❌ Failed to import MCP SDK:', error.message);
    console.log('💡 Try: npm install @modelcontextprotocol/sdk');
  });
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
