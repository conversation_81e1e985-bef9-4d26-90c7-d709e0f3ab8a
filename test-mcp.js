#!/usr/bin/env node

/**
 * Interactive test script for the Thinking Patterns MCP Server
 */

import { spawn } from "child_process";
import { createInterface } from "readline";

console.log("🚀 Testing Thinking Patterns MCP Server...\n");

class MCPTester {
  constructor() {
    this.server = null;
    this.testId = 1;
  }

  async startServer() {
    console.log("📡 Starting MCP Server...");
    this.server = spawn("node", ["mcp-thinking.js"], {
      stdio: ["pipe", "pipe", "pipe"],
    });

    this.server.stderr.on("data", (data) => {
      console.log("Server:", data.toString().trim());
    });

    this.server.on("error", (error) => {
      console.error("Server error:", error);
    });

    // Wait a moment for server to start
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return this.server;
  }

  async sendRequest(request) {
    return new Promise((resolve, reject) => {
      let response = "";

      const timeout = setTimeout(() => {
        reject(new Error("Request timeout"));
      }, 5000);

      this.server.stdout.once("data", (data) => {
        clearTimeout(timeout);
        response = data.toString();
        try {
          const parsed = JSON.parse(response);
          resolve(parsed);
        } catch (e) {
          resolve(response);
        }
      });

      this.server.stdin.write(JSON.stringify(request) + "\n");
    });
  }

  async runTests() {
    try {
      await this.startServer();
      console.log("✅ Server started successfully!\n");

      // Test 1: List tools
      console.log("🔧 Test 1: Listing available tools...");
      const listToolsRequest = {
        jsonrpc: "2.0",
        id: this.testId++,
        method: "tools/list",
      };

      try {
        const response = await this.sendRequest(listToolsRequest);
        console.log("✅ Tools listed successfully");
        console.log(`Found ${response.result?.tools?.length || 0} tools\n`);
      } catch (error) {
        console.log("❌ Failed to list tools:", error.message);
      }

      // Test 2: Get code quality rules
      console.log("🔧 Test 2: Getting code quality rules...");
      const codeQualityRequest = {
        jsonrpc: "2.0",
        id: this.testId++,
        method: "tools/call",
        params: {
          name: "get_code_quality_rules",
          arguments: {
            category: "function_length",
          },
        },
      };

      try {
        const response = await this.sendRequest(codeQualityRequest);
        console.log("✅ Code quality rules retrieved successfully\n");
      } catch (error) {
        console.log("❌ Failed to get code quality rules:", error.message);
      }

      // Test 3: Validate code
      console.log("🔧 Test 3: Validating code snippet...");
      const validateCodeRequest = {
        jsonrpc: "2.0",
        id: this.testId++,
        method: "tools/call",
        params: {
          name: "validate_code_against_patterns",
          arguments: {
            code: "function calc(a, b, c, d, e) {\n  return a + b + c + d + e;\n}",
            language: "javascript",
          },
        },
      };

      try {
        const response = await this.sendRequest(validateCodeRequest);
        console.log("✅ Code validation completed successfully\n");
      } catch (error) {
        console.log("❌ Failed to validate code:", error.message);
      }
    } catch (error) {
      console.error("❌ Test failed:", error);
    } finally {
      if (this.server) {
        this.server.kill();
        console.log("🛑 Server stopped");
      }
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new MCPTester();
  tester.runTests().catch(console.error);
}
