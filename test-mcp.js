#!/usr/bin/env node

/**
 * Simple test script for the Thinking Patterns MCP Server
 */

console.log('🚀 Testing Thinking Patterns MCP Server...\n');

// Test 1: Basic server startup
console.log('📡 Test 1: Starting server...');
console.log('Run: node mcp-thinking.js');
console.log('Expected: "Thinking Patterns MCP server running on stdio"\n');

// Test 2: Manual JSON-RPC test
console.log('🔧 Test 2: Manual JSON-RPC Test');
console.log('You can test the server manually by running:');
console.log('node mcp-thinking.js');
console.log('Then send JSON-RPC requests like:\n');

const testRequests = [
  {
    name: 'List Tools',
    request: {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list'
    }
  },
  {
    name: 'Get Code Quality Rules',
    request: {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'get_code_quality_rules',
        arguments: {
          category: 'function_length'
        }
      }
    }
  },
  {
    name: 'Validate Code',
    request: {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'validate_code_against_patterns',
        arguments: {
          code: 'function calc(a, b, c, d, e) { return a + b + c + d + e; }',
          language: 'javascript'
        }
      }
    }
  }
];

testRequests.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}:`);
  console.log(JSON.stringify(test.request, null, 2));
  console.log('');
});

console.log('💡 Quick Test Commands:');
console.log('1. Start server: node mcp-thinking.js');
console.log('2. In another terminal, test with curl or direct JSON input');
console.log('3. Check server logs for responses\n');

console.log('✅ Test script ready. Follow the manual steps above to test the MCP server.');
