#!/bin/bash

echo "🚀 Manual MCP Server Testing Guide"
echo "=================================="
echo ""

echo "1. 📡 Start the server in one terminal:"
echo "   node mcp-thinking.js"
echo ""

echo "2. 🔧 In another terminal, test with these commands:"
echo ""

echo "   Test 1 - List Tools:"
echo '   echo '\''{"jsonrpc":"2.0","id":1,"method":"tools/list"}'\'' | node mcp-thinking.js'
echo ""

echo "   Test 2 - Get Code Quality Rules:"
echo '   echo '\''{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"get_code_quality_rules","arguments":{"category":"function_length"}}}'\'' | node mcp-thinking.js'
echo ""

echo "   Test 3 - Validate Code:"
echo '   echo '\''{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"validate_code_against_patterns","arguments":{"code":"function calc(a,b,c,d,e){return a+b+c+d+e;}","language":"javascript"}}}'\'' | node mcp-thinking.js'
echo ""

echo "   Test 4 - Get Error Handling Strategy:"
echo '   echo '\''{"jsonrpc":"2.0","id":4,"method":"tools/call","params":{"name":"get_error_handling_strategy","arguments":{"context":"user_facing","language":"javascript"}}}'\'' | node mcp-thinking.js'
echo ""

echo "3. 🧪 Quick automated test:"
echo "   chmod +x manual-test.sh"
echo "   ./run-quick-test.sh"
echo ""

echo "4. 🔍 Interactive test:"
echo "   node test-mcp.js"
echo ""

echo "✅ Choose any method above to test your MCP server!"
