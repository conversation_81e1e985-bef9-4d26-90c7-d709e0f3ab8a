# Thinking Patterns MCP Server

A Model Context Protocol (MCP) server that exposes universal coding patterns and best practices as tools for AI assistants. This server provides access to battle-tested software engineering principles that apply across programming languages and frameworks.

## Features

### 🎯 **10 Powerful Tools Available**

1. **`get_code_quality_rules`** - Get specific code quality and readability rules with metrics
2. **`get_error_handling_strategy`** - Get error handling patterns for different contexts and languages
3. **`get_function_design_rules`** - Get function design principles including parameters, SRP, and purity
4. **`get_data_structure_guidance`** - Get data structure selection guidance based on use case
5. **`get_testing_strategy`** - Get ROI-optimized testing strategies and pyramid guidelines
6. **`get_architecture_patterns`** - Get code organization and architecture patterns
7. **`get_performance_guidelines`** - Get performance optimization strategies and caching patterns
8. **`get_security_patterns`** - Get security-first development patterns and reliability engineering
9. **`get_implementation_checklist`** - Get implementation checklists for different phases
10. **`validate_code_against_patterns`** - Validate code snippet against thinking patterns

### 📊 **Evidence-Based Patterns**

All patterns include empirical data and metrics:
- **60% better comprehension** with descriptive naming
- **3x more mental effort** for functions >20 lines
- **45% reduction** in support tickets with clear error messages
- **10x ROI** for unit tests vs E2E tests
- **40% faster onboarding** with feature-based organization

## Installation

### Prerequisites
- Node.js 18.0.0 or higher
- npm or yarn

### Setup
```bash
# Navigate to MCP directory
cd mcp/

# Install dependencies
npm install

# Make the script executable
chmod +x mcp-thinking.js

# Test the server
node mcp-thinking.js
```

## Usage Examples

### 1. Get Code Quality Rules
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_code_quality_rules",
    "arguments": {
      "category": "function_length"
    }
  }
}
```

**Response:**
```json
{
  "title": "Function Length Standards",
  "rules": [
    "Optimal Range: 5-15 lines per function",
    "Hard Limit: 20 lines maximum",
    "Rationale: Functions >20 lines require 3x more mental effort to understand"
  ],
  "metrics": "Functions >20 lines require 3x more mental effort"
}
```

### 2. Get Error Handling Strategy
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_error_handling_strategy",
    "arguments": {
      "context": "user_facing",
      "language": "javascript"
    }
  }
}
```

### 3. Validate Code Against Patterns
```json
{
  "method": "tools/call",
  "params": {
    "name": "validate_code_against_patterns",
    "arguments": {
      "code": "function calc(a, b, c, d, e) { ... }",
      "language": "javascript",
      "focus_areas": ["readability", "function_design"]
    }
  }
}
```

**Response:**
```json
{
  "analysis": {
    "function_length": "GOOD: Function length within optimal range",
    "naming": "WARNING: Function name 'calc' is not descriptive"
  },
  "recommendations": [
    "Use descriptive function names like 'calculateTotal' instead of 'calc'",
    "Consider reducing parameter count (currently 5, optimal is 2-3)"
  ],
  "overall_score": "60/100",
  "grade": "GOOD"
}
```

## Tool Categories

### 📋 **Code Quality & Readability**
- Function length standards (5-15 lines optimal)
- Variable naming requirements (descriptive over abbreviated)
- Nesting limits (max 3 levels)
- Comment strategies (explain "why" not "what")

### ⚠️ **Error Handling**
- Context-specific strategies (user-facing vs system/library)
- Language-specific patterns (Python, JavaScript, Rust, Go, Java)
- 5 universal error handling rules
- Circuit breaker implementations

### 🏗️ **Function & Architecture Design**
- Parameter optimization (2-3 sweet spot)
- Single Responsibility Principle validation
- Pure vs impure function strategies
- Data structure selection matrix
- Feature-based organization patterns

### 🧪 **Testing & Performance**
- Testing pyramid (70/20/10 distribution)
- Coverage vs quality guidelines (80% sweet spot)
- TDD vs BDD contextual strategies
- Performance optimization frameworks
- Caching strategy matrices

### 🔒 **Security & Reliability**
- Input validation patterns
- Principle of least privilege
- Secure by default approaches
- Reliability engineering patterns

## Integration with AI Assistants

This MCP server is designed to be used by AI coding assistants to:

1. **Provide Consistent Guidance**: All AI assistants using this server will give consistent advice based on proven patterns
2. **Evidence-Based Recommendations**: Every suggestion comes with empirical data and metrics
3. **Context-Aware Advice**: Different strategies for different contexts (user-facing vs system code)
4. **Language-Agnostic Principles**: Universal patterns that work across programming languages
5. **Practical Implementation**: Specific checklists and validation tools

## Configuration

Add to your MCP client configuration:

```json
{
  "mcpServers": {
    "thinking-patterns": {
      "command": "node",
      "args": ["/path/to/mcp/mcp-thinking.js"],
      "env": {}
    }
  }
}
```

## Contributing

This server is based on the universal thinking patterns documented in `thinking-patterns.md`. To contribute:

1. Update patterns in `exploration-patterns.md`
2. Refine and approve patterns in `thinking-patterns.md`
3. Update the MCP server implementation in `mcp-thinking.js`
4. Test with real AI assistant interactions

## License

MIT License - Feel free to use, modify, and distribute.

---

*This MCP server transforms universal coding wisdom into actionable tools for AI assistants, ensuring consistent, evidence-based guidance across all development tasks.*
